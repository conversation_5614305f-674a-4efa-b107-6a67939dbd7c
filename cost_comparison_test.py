import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from datetime import datetime
import argparse
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from utils.common import load_model
from config import ENV_CONFIG, AGENT_CONFIG, MODEL_PATHS, DEVICE

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'STFangsong', 'FangSong', 'SimSun', 'FangSongP']
plt.rcParams['axes.unicode_minus'] = False

def load_optimal_costs(csv_file_path):
    """加载CSV文件中的最优成本数据"""
    try:
        df = pd.read_csv(csv_file_path)
        if 'PeriodCost_Dollar' in df.columns:
            return df['PeriodCost_Dollar'].values
        else:
            # 如果没有PeriodCost_Dollar列，使用最后一列
            return df.iloc[:, -1].values
    except Exception as e:
        print(f"加载最优成本数据失败: {e}")
        return None

def run_cost_comparison_test(start_time_step=0, num_time_steps=96):
    """
    运行成本对比测试
    
    Args:
        start_time_step: 起始时间步
        num_time_steps: 测试的时间步数量（默认96个时间步）
    """
    print(f"使用设备: {DEVICE}")
    print(f"测试时间段: 第{start_time_step}到第{start_time_step + num_time_steps - 1}个时间步")
    
    # 1. 初始化环境
    env_config = ENV_CONFIG.copy()
    env_config['debug'] = False  # 关闭调试输出以减少干扰
    env = IEEE30Env(**env_config)
    
    # 2. 计算状态和动作维度
    state = env.reset()
    state_dim = len(state)
    action_dim = 5 * 16  # 5个发电机 * 16个时间步
    
    print(f"状态维度: {state_dim}")
    print(f"动作维度: {action_dim}")
    
    # 3. 初始化图构建器
    graph_builder = IEEE30GraphBuilder(
        num_buses=30, 
        num_branches=41, 
        num_gens=6
    )
    
    # 4. 初始化GNN-DDPG智能体
    agent = GNNDDPGAgent(
        node_features=3,  # 负荷、是否有发电机、发电机出力
        edge_features=1,  # 支路潮流
        action_dim=action_dim,
        action_bound=1.0,
        time_steps=16,
        hidden_dim=AGENT_CONFIG['hidden_size'] // 8,
        config=AGENT_CONFIG
    )
    
    # 5. 加载最佳模型
    if not load_model(agent, MODEL_PATHS['model_dir'], load_best=True):
        print("错误: 未找到最佳模型权重文件")
        return
    else:
        print("已成功加载最佳模型权重")
    
    # 6. 加载CSV文件中的最优成本数据
    csv_file_path = env_config.get('gen_schedule_file', 'data/power_schedule_with_wind_sequential.csv')
    optimal_costs = load_optimal_costs(csv_file_path)
    
    if optimal_costs is None:
        print("无法加载最优成本数据，测试终止")
        return
    
    # 7. 运行测试并收集数据
    model_costs = []
    optimal_costs_segment = []
    time_steps = []
    
    print(f"\n开始运行{num_time_steps}个时间步的成本对比测试...")
    print("-" * 60)
    
    # 设置环境的起始时间步
    env.current_time_step = start_time_step
    state = env.reset()
    
    for step in range(num_time_steps):
        current_time_step = start_time_step + step
        
        # 确保时间步在有效范围内
        if current_time_step >= len(optimal_costs):
            current_time_step = current_time_step % len(optimal_costs)
        
        # 使用最佳模型选择动作（不添加噪声）
        action = agent.select_action(state, graph_builder, add_noise=False)
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 收集成本数据
        if info and 'gen_cost' in info:
            model_cost = info['gen_cost']
            optimal_cost = optimal_costs[current_time_step]
            
            model_costs.append(model_cost)
            optimal_costs_segment.append(optimal_cost)
            time_steps.append(current_time_step)
            
            # 每10步显示一次进度
            if (step + 1) % 10 == 0:
                print(f"时间步 {current_time_step:3d}: 模型成本={model_cost:8.2f}, 最优成本={optimal_cost:8.2f}, 差异={model_cost-optimal_cost:8.2f}")
        
        # 更新状态
        if next_state is not None:
            state = next_state
        
        # 如果环境结束，重置环境
        if done:
            state = env.reset()
    
    print("-" * 60)
    print(f"测试完成，共收集了{len(model_costs)}个时间步的数据")
    
    # 8. 计算统计信息
    model_costs = np.array(model_costs)
    optimal_costs_segment = np.array(optimal_costs_segment)
    cost_differences = model_costs - optimal_costs_segment
    
    print(f"\n成本统计信息:")
    print(f"模型平均成本: {np.mean(model_costs):.2f}")
    print(f"最优平均成本: {np.mean(optimal_costs_segment):.2f}")
    print(f"平均成本差异: {np.mean(cost_differences):.2f}")
    print(f"成本差异标准差: {np.std(cost_differences):.2f}")
    print(f"最大成本差异: {np.max(cost_differences):.2f}")
    print(f"最小成本差异: {np.min(cost_differences):.2f}")
    
    # 9. 生成对比图表
    generate_cost_comparison_plot(
        time_steps, 
        model_costs, 
        optimal_costs_segment, 
        start_time_step, 
        num_time_steps
    )
    
    return {
        'time_steps': time_steps,
        'model_costs': model_costs,
        'optimal_costs': optimal_costs_segment,
        'cost_differences': cost_differences,
        'statistics': {
            'model_mean': np.mean(model_costs),
            'optimal_mean': np.mean(optimal_costs_segment),
            'diff_mean': np.mean(cost_differences),
            'diff_std': np.std(cost_differences),
            'diff_max': np.max(cost_differences),
            'diff_min': np.min(cost_differences)
        }
    }

def generate_cost_comparison_plot(time_steps, model_costs, optimal_costs, start_time, num_steps):
    """生成成本对比图表"""
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 子图1: 成本对比曲线
    ax1.plot(time_steps, model_costs, 'b-', linewidth=2, label='模型预测成本', marker='o', markersize=3)
    ax1.plot(time_steps, optimal_costs, 'r--', linewidth=2, label='CSV最优成本', marker='s', markersize=3)
    
    ax1.set_xlabel('时间步')
    ax1.set_ylabel('运行成本 (美元)')
    ax1.set_title(f'逐时段运行成本对比 (时间步 {start_time}-{start_time + num_steps - 1})')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 设置x轴刻度
    if len(time_steps) <= 24:
        ax1.set_xticks(time_steps[::2])  # 每2个时间步显示一个刻度
    else:
        ax1.set_xticks(time_steps[::8])  # 每8个时间步显示一个刻度
    
    # 子图2: 成本差异
    cost_diff = np.array(model_costs) - np.array(optimal_costs)
    colors = ['red' if x > 0 else 'green' for x in cost_diff]
    
    bars = ax2.bar(time_steps, cost_diff, color=colors, alpha=0.7, width=0.8)
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)
    ax2.set_xlabel('时间步')
    ax2.set_ylabel('成本差异 (美元)')
    ax2.set_title('模型成本与最优成本的差异 (正值表示模型成本更高)')
    ax2.grid(True, alpha=0.3)
    
    # 设置x轴刻度
    if len(time_steps) <= 24:
        ax2.set_xticks(time_steps[::2])
    else:
        ax2.set_xticks(time_steps[::8])
    
    # 添加统计信息文本框
    stats_text = f'平均差异: {np.mean(cost_diff):.2f}\n标准差: {np.std(cost_diff):.2f}\n最大差异: {np.max(cost_diff):.2f}\n最小差异: {np.min(cost_diff):.2f}'
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'cost_comparison_{start_time}_{start_time + num_steps - 1}_{timestamp}.png'
    output_path = os.path.join('plots', filename)
    
    # 确保输出目录存在
    os.makedirs('plots', exist_ok=True)
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n成本对比图表已保存到: {output_path}")

def main():
    """主函数，支持命令行参数"""
    parser = argparse.ArgumentParser(description='运行最佳模型的成本对比测试')
    parser.add_argument('--start', type=int, default=0, help='起始时间步 (默认: 0)')
    parser.add_argument('--steps', type=int, default=96, help='测试时间步数量 (默认: 96)')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("最佳模型逐时段运行成本对比测试")
    print("=" * 60)
    
    # 运行测试
    results = run_cost_comparison_test(
        start_time_step=args.start,
        num_time_steps=args.steps
    )
    
    if results:
        print("\n测试完成！")
        print("=" * 60)

if __name__ == "__main__":
    main()

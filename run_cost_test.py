"""
成本对比测试启动器
提供多种方式启动成本对比测试
"""

import sys
import os

def show_main_menu():
    """显示主菜单"""
    print("=" * 60)
    print("🎯 最佳模型成本对比测试系统")
    print("=" * 60)
    print(f"📊 数据概览: 17473个时间点 (约182天)")
    print(f"🕐 时间分辨率: 15分钟间隔")
    print(f"🎯 测试目标: 对比模型成本与CSV最优成本")
    
    print(f"\n🚀 请选择测试方式:")
    print(f"1. 智能时间选择器 (推荐) - 直观选择任意时间点")
    print(f"2. 交互式测试 - 改进版界面")
    print(f"3. 快速测试 - 预设参数快速运行")
    print(f"4. 命令行测试 - 支持参数调用")
    print(f"5. 查看使用说明")
    print(f"0. 退出")
    
    while True:
        try:
            choice = int(input(f"\n请选择 (0-5): "))
            return choice
        except ValueError:
            print("❌ 请输入有效的数字")

def run_smart_selector():
    """运行智能时间选择器"""
    print(f"\n🚀 启动智能时间选择器...")
    try:
        import smart_time_selector
        smart_time_selector.main()
    except ImportError:
        print("❌ 找不到 smart_time_selector.py 文件")
    except Exception as e:
        print(f"❌ 运行出错: {e}")

def run_interactive_test():
    """运行交互式测试"""
    print(f"\n🚀 启动交互式测试...")
    try:
        import interactive_cost_test
        interactive_cost_test.main()
    except ImportError:
        print("❌ 找不到 interactive_cost_test.py 文件")
    except Exception as e:
        print(f"❌ 运行出错: {e}")

def run_quick_test():
    """运行快速测试"""
    print(f"\n🚀 启动快速测试...")
    print(f"💡 提示: 可以修改 quick_cost_test.py 中的参数来改变测试时间段")
    try:
        import quick_cost_test
        quick_cost_test.main()
    except ImportError:
        print("❌ 找不到 quick_cost_test.py 文件")
    except Exception as e:
        print(f"❌ 运行出错: {e}")

def run_command_test():
    """运行命令行测试"""
    print(f"\n🚀 命令行测试选项:")
    print(f"💡 您可以使用以下命令:")
    print(f"   python cost_comparison_test.py                    # 默认参数")
    print(f"   python cost_comparison_test.py --start 0 --steps 96   # 自定义参数")
    print(f"   python cost_comparison_test.py --start 1000 --steps 48  # 从第1000个时间点开始")
    
    print(f"\n常用时间点参考:")
    print(f"   时间点 0    -> 第1天 00:00")
    print(f"   时间点 28   -> 第1天 07:00 (早高峰)")
    print(f"   时间点 72   -> 第1天 18:00 (晚高峰)")
    print(f"   时间点 96   -> 第2天 00:00")
    print(f"   时间点 1000 -> 第11天 10:00")
    
    run_now = input(f"\n是否现在运行默认测试 (从时间点0开始，96个时间点)? (y/n): ").lower().strip()
    if run_now == 'y':
        try:
            import cost_comparison_test
            # 模拟命令行参数
            sys.argv = ['cost_comparison_test.py']
            cost_comparison_test.main()
        except ImportError:
            print("❌ 找不到 cost_comparison_test.py 文件")
        except Exception as e:
            print(f"❌ 运行出错: {e}")

def show_usage_guide():
    """显示使用说明"""
    print(f"\n📖 使用说明")
    print("=" * 60)
    
    print(f"\n🎯 测试程序说明:")
    print(f"1. 智能时间选择器 (smart_time_selector.py)")
    print(f"   - 最推荐使用")
    print(f"   - 提供直观的时间选择界面")
    print(f"   - 支持按日期时间、时间点编号、特殊时间段等多种方式选择")
    print(f"   - 可以随机选择时间点")
    print(f"   - 选择后可直接运行测试")
    
    print(f"\n2. 交互式测试 (interactive_cost_test.py)")
    print(f"   - 改进的交互界面")
    print(f"   - 支持智能时间选择")
    print(f"   - 生成详细的4子图分析")
    print(f"   - 可保存详细结果到CSV")
    
    print(f"\n3. 快速测试 (quick_cost_test.py)")
    print(f"   - 预设参数，快速验证")
    print(f"   - 适合日常快速检查")
    print(f"   - 可修改代码中的参数")
    
    print(f"\n4. 命令行测试 (cost_comparison_test.py)")
    print(f"   - 支持命令行参数")
    print(f"   - 适合批量测试和脚本调用")
    print(f"   - 用法: python cost_comparison_test.py --start 起始点 --steps 时间步数")
    
    print(f"\n🕐 时间点对应关系:")
    print(f"   每个时间点 = 15分钟")
    print(f"   每小时 = 4个时间点")
    print(f"   每天 = 96个时间点")
    print(f"   时间点计算公式: (天数-1) × 96 + 小时 × 4 + 分钟÷15")
    
    print(f"\n📊 输出内容:")
    print(f"   - 成本对比曲线图")
    print(f"   - 成本差异分析")
    print(f"   - 统计指标 (平均差异、相对误差等)")
    print(f"   - 图表保存到 plots/ 目录")
    print(f"   - 可选保存详细数据到CSV")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 首次使用: 选择智能时间选择器")
    print(f"   2. 快速验证: 使用快速测试")
    print(f"   3. 详细分析: 使用交互式测试")
    print(f"   4. 批量测试: 使用命令行测试")
    
    print(f"\n📁 文件要求:")
    print(f"   - models/best_gnn_model.pth (最佳模型文件)")
    print(f"   - data/power_schedule_with_wind_sequential.csv (最优成本数据)")
    print(f"   - data/active_load_profile.csv (负荷数据)")

def main():
    """主函数"""
    while True:
        choice = show_main_menu()
        
        if choice == 0:
            print("👋 再见!")
            break
        elif choice == 1:
            run_smart_selector()
        elif choice == 2:
            run_interactive_test()
        elif choice == 3:
            run_quick_test()
        elif choice == 4:
            run_command_test()
        elif choice == 5:
            show_usage_guide()
        else:
            print("❌ 请选择0-5之间的选项")
        
        if choice != 5:  # 如果不是查看说明，询问是否继续
            continue_test = input(f"\n是否继续使用其他测试方式? (y/n): ").lower().strip()
            if continue_test != 'y':
                print("👋 再见!")
                break

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试早停机制配置的简单脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import TRAIN_CONFIG

def test_early_stop_config():
    """测试早停机制配置"""
    print("=" * 60)
    print("早停机制配置测试")
    print("=" * 60)
    
    # 检查配置项
    early_stop_enabled = TRAIN_CONFIG.get('early_stop_enabled', False)
    cost_ratio_threshold = TRAIN_CONFIG.get('cost_ratio_threshold', 0.9)
    consecutive_episodes_threshold = TRAIN_CONFIG.get('consecutive_episodes_threshold', 30)
    
    print(f"早停机制启用: {early_stop_enabled}")
    print(f"成本比阈值: {cost_ratio_threshold}")
    print(f"连续回合阈值: {consecutive_episodes_threshold}")
    print(f"总训练回合数: {TRAIN_CONFIG['num_episodes']}")
    
    # 验证配置合理性
    print("\n" + "=" * 60)
    print("配置验证")
    print("=" * 60)
    
    if early_stop_enabled:
        print("✅ 早停机制已启用")
        
        if 0 < cost_ratio_threshold < 1:
            print(f"✅ 成本比阈值 {cost_ratio_threshold} 在合理范围内 (0, 1)")
        else:
            print(f"⚠️  成本比阈值 {cost_ratio_threshold} 可能不合理，建议设置在 (0, 1) 范围内")
        
        if 1 <= consecutive_episodes_threshold <= TRAIN_CONFIG['num_episodes']:
            print(f"✅ 连续回合阈值 {consecutive_episodes_threshold} 在合理范围内")
        else:
            print(f"⚠️  连续回合阈值 {consecutive_episodes_threshold} 可能不合理")
            
        # 计算最早可能的停止回合
        earliest_stop = consecutive_episodes_threshold
        print(f"📊 最早可能停止的回合: 第 {earliest_stop} 回合")
        print(f"📊 最晚停止回合: 第 {TRAIN_CONFIG['num_episodes']} 回合")
        
    else:
        print("ℹ️  早停机制未启用，将进行完整训练")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

def simulate_early_stop_scenario():
    """模拟早停场景"""
    print("\n" + "=" * 60)
    print("早停场景模拟")
    print("=" * 60)
    
    if not TRAIN_CONFIG.get('early_stop_enabled', False):
        print("早停机制未启用，跳过模拟")
        return
    
    cost_ratio_threshold = TRAIN_CONFIG.get('cost_ratio_threshold', 0.9)
    consecutive_threshold = TRAIN_CONFIG.get('consecutive_episodes_threshold', 30)
    
    # 模拟一些成本比数据
    import random
    random.seed(42)  # 固定随机种子以便重现
    
    print(f"模拟场景: 成本比阈值 = {cost_ratio_threshold}, 连续阈值 = {consecutive_threshold}")
    print("\n模拟成本比序列:")
    
    consecutive_count = 0
    for episode in range(1, 51):  # 模拟50个回合
        # 生成随机成本比，前20回合较低，后面逐渐升高
        if episode <= 20:
            cost_ratio = random.uniform(0.7, 0.85)
        else:
            cost_ratio = random.uniform(0.85, 0.95)
        
        if cost_ratio > cost_ratio_threshold:
            consecutive_count += 1
            status = f"⚠️  连续计数: {consecutive_count}"
        else:
            consecutive_count = 0
            status = "✅ 重置计数"
        
        print(f"回合 {episode:2d}: 成本比 = {cost_ratio:.4f} | {status}")
        
        # 检查是否触发早停
        if consecutive_count >= consecutive_threshold:
            print(f"\n🛑 早停触发! 在第 {episode} 回合")
            break
    else:
        print(f"\n✅ 模拟完成，未触发早停 (最大连续计数: {consecutive_count})")

if __name__ == "__main__":
    test_early_stop_config()
    simulate_early_stop_scenario()

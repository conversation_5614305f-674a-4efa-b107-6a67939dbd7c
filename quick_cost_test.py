"""
快速成本对比测试脚本
用于快速测试最佳模型在指定时间段的成本表现
"""

import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from agents.gnn.graph_utils import IEEE30GraphBuilder
from utils.common import load_model
from config import ENV_CONFIG, AGENT_CONFIG, MODEL_PATHS, DEVICE

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False

def quick_cost_comparison(start_hour=0, duration_hours=4):
    """
    快速成本对比测试
    
    Args:
        start_hour: 起始小时 (0-23)
        duration_hours: 测试持续小时数
    """
    print(f"快速成本对比测试")
    print(f"起始时间: 第{start_hour}小时")
    print(f"测试时长: {duration_hours}小时 ({duration_hours * 4}个时间步)")
    print(f"使用设备: {DEVICE}")
    print("-" * 50)
    
    # 1. 初始化环境
    env_config = ENV_CONFIG.copy()
    env_config['debug'] = False
    env = IEEE30Env(**env_config)
    
    # 2. 初始化智能体
    state = env.reset()
    state_dim = len(state)
    action_dim = 5 * 16
    
    graph_builder = IEEE30GraphBuilder(num_buses=30, num_branches=41, num_gens=6)
    
    agent = GNNDDPGAgent(
        node_features=3,
        edge_features=1,
        action_dim=action_dim,
        action_bound=1.0,
        time_steps=16,
        hidden_dim=AGENT_CONFIG['hidden_size'] // 8,
        config=AGENT_CONFIG
    )
    
    # 3. 加载最佳模型
    if not load_model(agent, MODEL_PATHS['model_dir'], load_best=True):
        print("错误: 未找到最佳模型")
        return None
    
    print("✓ 已加载最佳模型")
    
    # 4. 加载最优成本数据
    csv_path = 'data/power_schedule_with_wind_sequential.csv'
    try:
        df = pd.read_csv(csv_path)
        optimal_costs = df['PeriodCost_Dollar'].values
        print(f"✓ 已加载最优成本数据 ({len(optimal_costs)}个时间点)")
    except Exception as e:
        print(f"✗ 加载最优成本数据失败: {e}")
        return None
    
    # 5. 运行测试
    start_step = start_hour * 4  # 每小时4个时间步
    num_steps = duration_hours * 4
    
    model_costs = []
    optimal_costs_test = []
    time_labels = []
    
    # 设置环境起始时间
    env.current_time_step = start_step
    state = env.reset()
    
    print(f"\n开始测试...")
    
    for step in range(num_steps):
        current_step = start_step + step
        
        # 处理时间步循环
        if current_step >= len(optimal_costs):
            current_step = current_step % len(optimal_costs)
        
        # 模型预测
        action = agent.select_action(state, graph_builder, add_noise=False)
        next_state, reward, done, info = env.step(action)
        
        # 收集数据
        if info and 'gen_cost' in info:
            model_cost = info['gen_cost']
            optimal_cost = optimal_costs[current_step]
            
            model_costs.append(model_cost)
            optimal_costs_test.append(optimal_cost)
            
            # 生成时间标签 (小时:15分钟间隔)
            hour = (start_hour + step // 4) % 24
            minute = (step % 4) * 15
            time_labels.append(f"{hour:02d}:{minute:02d}")
            
            print(f"  {time_labels[-1]}: 模型={model_cost:6.1f}, 最优={optimal_cost:6.1f}, 差异={model_cost-optimal_cost:+6.1f}")
        
        # 更新状态
        if next_state is not None:
            state = next_state
        if done:
            state = env.reset()
    
    # 6. 计算统计信息
    model_costs = np.array(model_costs)
    optimal_costs_test = np.array(optimal_costs_test)
    differences = model_costs - optimal_costs_test
    
    print(f"\n统计结果:")
    print(f"  模型平均成本: {np.mean(model_costs):8.2f}")
    print(f"  最优平均成本: {np.mean(optimal_costs_test):8.2f}")
    print(f"  平均成本差异: {np.mean(differences):8.2f}")
    print(f"  成本差异范围: [{np.min(differences):6.2f}, {np.max(differences):6.2f}]")
    print(f"  相对误差: {np.mean(differences)/np.mean(optimal_costs_test)*100:6.2f}%")
    
    # 7. 生成图表
    create_quick_plot(time_labels, model_costs, optimal_costs_test, start_hour, duration_hours)
    
    return {
        'model_costs': model_costs,
        'optimal_costs': optimal_costs_test,
        'differences': differences,
        'time_labels': time_labels
    }

def create_quick_plot(time_labels, model_costs, optimal_costs, start_hour, duration_hours):
    """创建快速对比图表"""
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 上图: 成本对比
    x_pos = range(len(time_labels))
    ax1.plot(x_pos, model_costs, 'b-o', linewidth=2, markersize=4, label='模型预测成本')
    ax1.plot(x_pos, optimal_costs, 'r--s', linewidth=2, markersize=4, label='CSV最优成本')
    
    ax1.set_title(f'逐时段运行成本对比 ({start_hour:02d}:00 - {(start_hour+duration_hours)%24:02d}:00)')
    ax1.set_ylabel('运行成本 (美元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 设置x轴标签
    step = max(1, len(time_labels) // 8)  # 最多显示8个标签
    ax1.set_xticks(x_pos[::step])
    ax1.set_xticklabels(time_labels[::step], rotation=45)
    
    # 下图: 成本差异
    differences = np.array(model_costs) - np.array(optimal_costs)
    colors = ['red' if x > 0 else 'green' for x in differences]
    
    ax2.bar(x_pos, differences, color=colors, alpha=0.7)
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)
    ax2.set_title('成本差异 (模型成本 - 最优成本)')
    ax2.set_xlabel('时间')
    ax2.set_ylabel('成本差异 (美元)')
    ax2.grid(True, alpha=0.3)
    
    ax2.set_xticks(x_pos[::step])
    ax2.set_xticklabels(time_labels[::step], rotation=45)
    
    # 添加统计信息
    stats_text = f'平均差异: {np.mean(differences):.1f}\n最大差异: {np.max(differences):.1f}\n最小差异: {np.min(differences):.1f}'
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, fontsize=9,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs('plots', exist_ok=True)
    filename = f'quick_cost_comparison_{start_hour}h_{duration_hours}h.png'
    filepath = os.path.join('plots', filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n图表已保存: {filepath}")

def main():
    """主函数 - 可以修改这里的参数来测试不同时间段"""
    
    # 可以修改这些参数来测试不同的时间段
    START_HOUR = 0      # 起始小时 (0-23)
    DURATION_HOURS = 4  # 测试持续小时数
    
    print("=" * 50)
    print("快速成本对比测试")
    print("=" * 50)
    
    results = quick_cost_comparison(
        start_hour=START_HOUR,
        duration_hours=DURATION_HOURS
    )
    
    if results:
        print("\n✓ 测试完成!")
    else:
        print("\n✗ 测试失败!")

if __name__ == "__main__":
    main()

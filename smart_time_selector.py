"""
智能时间点选择器
帮助用户直观地选择17473个时间点中的任意时间点进行成本对比测试
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False

class SmartTimeSelector:
    def __init__(self):
        self.total_time_points = 17473
        self.points_per_day = 96  # 每天96个时间点（15分钟间隔）
        self.total_days = self.total_time_points // self.points_per_day
        self.remaining_points = self.total_time_points % self.points_per_day
        
        print(f"📊 数据概览:")
        print(f"  总时间点数: {self.total_time_points}")
        print(f"  完整天数: {self.total_days}天")
        print(f"  剩余时间点: {self.remaining_points}个")
        print(f"  时间分辨率: 15分钟间隔")
        print(f"  数据总时长: 约{self.total_days + (self.remaining_points/96):.1f}天")
    
    def time_point_to_datetime(self, time_point):
        """将时间点转换为日期时间"""
        day = time_point // 96
        hour = (time_point % 96) // 4
        minute = (time_point % 4) * 15
        return f"第{day+1}天 {hour:02d}:{minute:02d}"
    
    def datetime_to_time_point(self, day, hour, minute):
        """将日期时间转换为时间点"""
        return (day - 1) * 96 + hour * 4 + minute // 15
    
    def show_selection_menu(self):
        """显示时间点选择菜单"""
        print("\n" + "="*60)
        print("🕐 智能时间点选择器")
        print("="*60)
        
        while True:
            print(f"\n请选择时间点选择方式:")
            print(f"1. 按天和时间选择 (推荐)")
            print(f"2. 直接输入时间点编号")
            print(f"3. 选择特殊时间段")
            print(f"4. 随机选择时间点")
            print(f"5. 查看数据分布")
            print(f"0. 退出")
            
            try:
                choice = int(input("\n请选择 (0-5): "))
                
                if choice == 0:
                    return None
                elif choice == 1:
                    return self.select_by_datetime()
                elif choice == 2:
                    return self.select_by_timepoint()
                elif choice == 3:
                    return self.select_special_periods()
                elif choice == 4:
                    return self.select_random()
                elif choice == 5:
                    self.show_data_distribution()
                    continue
                else:
                    print("❌ 请选择0-5之间的选项")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def select_by_datetime(self):
        """按日期时间选择"""
        print(f"\n📅 按日期时间选择时间点")
        print(f"可选范围: 第1天到第{self.total_days}天")
        
        # 选择天数
        while True:
            try:
                day = int(input(f"请输入天数 (1-{self.total_days}): "))
                if 1 <= day <= self.total_days:
                    break
                else:
                    print(f"❌ 请输入1到{self.total_days}之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        # 选择小时
        while True:
            try:
                hour = int(input("请输入小时 (0-23): "))
                if 0 <= hour <= 23:
                    break
                else:
                    print("❌ 请输入0到23之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        # 选择分钟（只能是0, 15, 30, 45）
        print("可选分钟: 0, 15, 30, 45")
        while True:
            try:
                minute = int(input("请输入分钟 (0/15/30/45): "))
                if minute in [0, 15, 30, 45]:
                    break
                else:
                    print("❌ 请输入0, 15, 30, 45中的一个")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        time_point = self.datetime_to_time_point(day, hour, minute)
        
        print(f"\n✅ 选择的时间点:")
        print(f"  时间点编号: {time_point}")
        print(f"  对应时间: {self.time_point_to_datetime(time_point)}")
        
        return time_point
    
    def select_by_timepoint(self):
        """直接输入时间点编号"""
        print(f"\n🔢 直接输入时间点编号")
        print(f"可选范围: 0 到 {self.total_time_points - 1}")
        
        while True:
            try:
                time_point = int(input(f"请输入时间点编号 (0-{self.total_time_points - 1}): "))
                if 0 <= time_point < self.total_time_points:
                    print(f"\n✅ 选择的时间点:")
                    print(f"  时间点编号: {time_point}")
                    print(f"  对应时间: {self.time_point_to_datetime(time_point)}")
                    return time_point
                else:
                    print(f"❌ 请输入0到{self.total_time_points - 1}之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def select_special_periods(self):
        """选择特殊时间段"""
        print(f"\n⭐ 特殊时间段选择")
        
        special_periods = {
            1: ("早高峰", 7, 0),    # 07:00
            2: ("上午", 10, 0),     # 10:00
            3: ("午高峰", 12, 0),   # 12:00
            4: ("下午", 15, 0),     # 15:00
            5: ("晚高峰", 18, 0),   # 18:00
            6: ("夜间", 22, 0),     # 22:00
            7: ("深夜", 2, 0),      # 02:00
            8: ("凌晨", 5, 0),      # 05:00
        }
        
        print("可选特殊时间段:")
        for key, (name, hour, minute) in special_periods.items():
            print(f"  {key}. {name} ({hour:02d}:{minute:02d})")
        
        while True:
            try:
                choice = int(input(f"请选择时间段 (1-{len(special_periods)}): "))
                if choice in special_periods:
                    name, hour, minute = special_periods[choice]
                    
                    # 选择哪一天
                    while True:
                        try:
                            day = int(input(f"请选择第几天 (1-{self.total_days}): "))
                            if 1 <= day <= self.total_days:
                                break
                            else:
                                print(f"❌ 请输入1到{self.total_days}之间的数字")
                        except ValueError:
                            print("❌ 请输入有效的数字")
                    
                    time_point = self.datetime_to_time_point(day, hour, minute)
                    
                    print(f"\n✅ 选择的时间点:")
                    print(f"  时间段: {name}")
                    print(f"  时间点编号: {time_point}")
                    print(f"  对应时间: {self.time_point_to_datetime(time_point)}")
                    
                    return time_point
                else:
                    print(f"❌ 请选择1到{len(special_periods)}之间的选项")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def select_random(self):
        """随机选择时间点"""
        print(f"\n🎲 随机选择时间点")
        
        # 生成5个随机时间点供选择
        random_points = np.random.choice(self.total_time_points, 5, replace=False)
        random_points = sorted(random_points)
        
        print("随机生成的时间点:")
        for i, point in enumerate(random_points, 1):
            print(f"  {i}. 时间点 {point} - {self.time_point_to_datetime(point)}")
        
        print(f"  6. 重新生成随机时间点")
        print(f"  0. 返回上级菜单")
        
        while True:
            try:
                choice = int(input("请选择 (0-6): "))
                if choice == 0:
                    return None
                elif choice == 6:
                    return self.select_random()  # 递归调用重新生成
                elif 1 <= choice <= 5:
                    selected_point = random_points[choice - 1]
                    print(f"\n✅ 选择的随机时间点:")
                    print(f"  时间点编号: {selected_point}")
                    print(f"  对应时间: {self.time_point_to_datetime(selected_point)}")
                    return selected_point
                else:
                    print("❌ 请选择0-6之间的选项")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def show_data_distribution(self):
        """显示数据分布信息"""
        print(f"\n📈 数据分布信息")
        print("-" * 50)
        
        # 按天分布
        print(f"📅 按天分布:")
        print(f"  完整天数: {self.total_days}天")
        print(f"  每天时间点: 96个 (00:00-23:45, 15分钟间隔)")
        print(f"  最后一天时间点: {self.remaining_points}个")
        
        # 时间点示例
        print(f"\n🕐 时间点对应关系示例:")
        examples = [0, 4, 28, 48, 72, 96, 192, 288]
        for point in examples:
            if point < self.total_time_points:
                print(f"  时间点 {point:4d} -> {self.time_point_to_datetime(point)}")
        
        # 特殊时间段的时间点
        print(f"\n⭐ 常用时间段对应的时间点:")
        periods = [
            ("早高峰 (07:00)", 28),
            ("午高峰 (12:00)", 48), 
            ("晚高峰 (18:00)", 72),
            ("夜间 (22:00)", 88),
            ("第二天开始", 96),
            ("第三天开始", 192),
        ]
        
        for name, base_point in periods:
            if base_point < self.total_time_points:
                print(f"  {name:<15} -> 时间点 {base_point} (第1天)")
                if base_point + 96 < self.total_time_points:
                    print(f"  {name:<15} -> 时间点 {base_point + 96} (第2天)")
        
        print(f"\n💡 提示:")
        print(f"  - 每96个时间点为一天")
        print(f"  - 时间点 = (天数-1) × 96 + 小时 × 4 + 分钟÷15")
        print(f"  - 例如: 第3天15:30 = 2×96 + 15×4 + 30÷15 = 254")

def run_cost_test_with_selected_time(start_time_point):
    """使用选定的时间点运行成本对比测试"""
    print(f"\n🚀 准备运行成本对比测试...")
    print(f"起始时间点: {start_time_point}")
    
    # 导入并运行交互式测试的核心功能
    try:
        from interactive_cost_test import run_interactive_test
        
        # 询问测试时长
        print(f"\n请选择测试时长:")
        print(f"1. 1小时 (4个时间点)")
        print(f"2. 4小时 (16个时间点)")
        print(f"3. 12小时 (48个时间点)")
        print(f"4. 24小时 (96个时间点)")
        print(f"5. 自定义")
        
        while True:
            try:
                choice = int(input("请选择 (1-5): "))
                if choice == 1:
                    num_steps = 4
                    break
                elif choice == 2:
                    num_steps = 16
                    break
                elif choice == 3:
                    num_steps = 48
                    break
                elif choice == 4:
                    num_steps = 96
                    break
                elif choice == 5:
                    max_steps = min(96, 17473 - start_time_point)
                    num_steps = int(input(f"请输入时间点数量 (1-{max_steps}): "))
                    if 1 <= num_steps <= max_steps:
                        break
                    else:
                        print(f"❌ 请输入1到{max_steps}之间的数字")
                else:
                    print("❌ 请选择1-5之间的选项")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        print(f"\n开始运行测试...")
        results = run_interactive_test(start_time_point, num_steps)
        
        if results:
            print(f"\n✅ 测试完成!")
        else:
            print(f"\n❌ 测试失败!")
            
    except ImportError:
        print(f"❌ 无法导入测试模块，请确保 interactive_cost_test.py 文件存在")
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")

def main():
    """主函数"""
    selector = SmartTimeSelector()
    
    while True:
        selected_time_point = selector.show_selection_menu()
        
        if selected_time_point is None:
            print("👋 再见!")
            break
        
        # 询问是否运行测试
        run_test = input(f"\n是否使用此时间点运行成本对比测试? (y/n): ").lower().strip()
        if run_test == 'y':
            run_cost_test_with_selected_time(selected_time_point)
        
        # 询问是否继续选择
        continue_select = input(f"\n是否继续选择其他时间点? (y/n): ").lower().strip()
        if continue_select != 'y':
            print("👋 再见!")
            break

if __name__ == "__main__":
    main()

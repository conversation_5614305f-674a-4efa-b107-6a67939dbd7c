"""
交互式成本对比测试脚本
允许用户选择起始时刻和测试时长
"""

import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from agents.gnn.graph_utils import IEEE30GraphBuilder
from utils.common import load_model
from config import ENV_CONFIG, AGENT_CONFIG, MODEL_PATHS, DEVICE

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'KaiTi']
plt.rcParams['axes.unicode_minus'] = False

def get_user_input():
    """获取用户输入的测试参数"""
    print("=" * 60)
    print("交互式成本对比测试")
    print("=" * 60)
    
    # 显示可用的时间范围
    try:
        df = pd.read_csv('data/power_schedule_with_wind_sequential.csv')
        total_time_points = len(df)
        total_days = total_time_points // 96  # 每天96个时间点
        print(f"可用数据: {total_time_points}个时间点 (约{total_days}天)")
        print(f"时间分辨率: 15分钟间隔")
    except:
        print("无法读取数据文件信息")
        total_time_points = 96
    
    print("\n请选择测试参数:")
    
    # 获取起始时间
    while True:
        try:
            print(f"\n1. 起始时间点 (0-{total_time_points-1}):")
            print("   提示: 0=00:00, 4=01:00, 96=第二天00:00")
            start_step = int(input("   请输入起始时间点: "))
            if 0 <= start_step < total_time_points:
                break
            else:
                print(f"   错误: 请输入0到{total_time_points-1}之间的数字")
        except ValueError:
            print("   错误: 请输入有效的数字")
    
    # 获取测试时长
    while True:
        try:
            print(f"\n2. 测试时长选项:")
            print("   1 - 1小时 (4个时间点)")
            print("   2 - 4小时 (16个时间点)")
            print("   3 - 12小时 (48个时间点)")
            print("   4 - 24小时 (96个时间点)")
            print("   5 - 自定义")
            
            choice = int(input("   请选择 (1-5): "))
            
            if choice == 1:
                num_steps = 4
                break
            elif choice == 2:
                num_steps = 16
                break
            elif choice == 3:
                num_steps = 48
                break
            elif choice == 4:
                num_steps = 96
                break
            elif choice == 5:
                max_steps = min(96, total_time_points - start_step)
                num_steps = int(input(f"   请输入时间点数量 (1-{max_steps}): "))
                if 1 <= num_steps <= max_steps:
                    break
                else:
                    print(f"   错误: 请输入1到{max_steps}之间的数字")
            else:
                print("   错误: 请选择1-5之间的选项")
        except ValueError:
            print("   错误: 请输入有效的数字")
    
    # 确认参数
    start_hour = start_step // 4
    start_minute = (start_step % 4) * 15
    duration_hours = num_steps / 4
    
    print(f"\n确认测试参数:")
    print(f"  起始时间: {start_hour:02d}:{start_minute:02d} (第{start_step}个时间点)")
    print(f"  测试时长: {duration_hours:.1f}小时 ({num_steps}个时间点)")
    print(f"  结束时间: 第{start_step + num_steps - 1}个时间点")
    
    confirm = input("\n确认开始测试? (y/n): ").lower().strip()
    if confirm != 'y':
        print("测试已取消")
        return None, None
    
    return start_step, num_steps

def run_interactive_test(start_step, num_steps):
    """运行交互式成本对比测试"""
    
    print(f"\n开始初始化...")
    print(f"使用设备: {DEVICE}")
    
    # 1. 初始化环境
    env_config = ENV_CONFIG.copy()
    env_config['debug'] = False
    env = IEEE30Env(**env_config)
    
    # 2. 初始化智能体
    state = env.reset()
    action_dim = 5 * 16
    
    graph_builder = IEEE30GraphBuilder(num_buses=30, num_branches=41, num_gens=6)
    
    agent = GNNDDPGAgent(
        node_features=3,
        edge_features=1,
        action_dim=action_dim,
        action_bound=1.0,
        time_steps=16,
        hidden_dim=AGENT_CONFIG['hidden_size'] // 8,
        config=AGENT_CONFIG
    )
    
    # 3. 加载最佳模型
    print("加载最佳模型...")
    if not load_model(agent, MODEL_PATHS['model_dir'], load_best=True):
        print("✗ 错误: 未找到最佳模型")
        return None
    print("✓ 最佳模型加载成功")
    
    # 4. 加载最优成本数据
    print("加载最优成本数据...")
    try:
        df = pd.read_csv('data/power_schedule_with_wind_sequential.csv')
        optimal_costs = df['PeriodCost_Dollar'].values
        print(f"✓ 最优成本数据加载成功 ({len(optimal_costs)}个时间点)")
    except Exception as e:
        print(f"✗ 加载最优成本数据失败: {e}")
        return None
    
    # 5. 运行测试
    print(f"\n开始运行测试...")
    print("-" * 60)
    
    model_costs = []
    optimal_costs_test = []
    time_labels = []
    detailed_info = []
    
    # 设置环境起始时间
    env.current_time_step = start_step
    state = env.reset()
    
    for step in range(num_steps):
        current_step = start_step + step
        
        # 处理时间步循环
        if current_step >= len(optimal_costs):
            current_step = current_step % len(optimal_costs)
        
        # 模型预测
        action = agent.select_action(state, graph_builder, add_noise=False)
        next_state, reward, done, info = env.step(action)
        
        # 收集数据
        if info and 'gen_cost' in info:
            model_cost = info['gen_cost']
            optimal_cost = optimal_costs[current_step]
            difference = model_cost - optimal_cost
            
            model_costs.append(model_cost)
            optimal_costs_test.append(optimal_cost)
            
            # 生成时间标签
            hour = ((start_step + step) // 4) % 24
            minute = ((start_step + step) % 4) * 15
            time_label = f"{hour:02d}:{minute:02d}"
            time_labels.append(time_label)
            
            # 保存详细信息
            detailed_info.append({
                'time_step': current_step,
                'time_label': time_label,
                'model_cost': model_cost,
                'optimal_cost': optimal_cost,
                'difference': difference,
                'relative_error': (difference / optimal_cost) * 100 if optimal_cost > 0 else 0
            })
            
            # 显示进度
            if (step + 1) % max(1, num_steps // 10) == 0 or step < 5 or step >= num_steps - 5:
                print(f"{time_label}: 模型={model_cost:7.1f}, 最优={optimal_cost:7.1f}, 差异={difference:+7.1f} ({difference/optimal_cost*100:+5.1f}%)")
        
        # 更新状态
        if next_state is not None:
            state = next_state
        if done:
            state = env.reset()
    
    print("-" * 60)
    
    # 6. 计算和显示统计信息
    model_costs = np.array(model_costs)
    optimal_costs_test = np.array(optimal_costs_test)
    differences = model_costs - optimal_costs_test
    relative_errors = (differences / optimal_costs_test) * 100
    
    print(f"\n📊 测试结果统计:")
    print(f"{'指标':<20} {'数值':<15}")
    print("-" * 35)
    print(f"{'模型平均成本':<20} {np.mean(model_costs):>10.2f}")
    print(f"{'最优平均成本':<20} {np.mean(optimal_costs_test):>10.2f}")
    print(f"{'平均成本差异':<20} {np.mean(differences):>10.2f}")
    print(f"{'成本差异标准差':<20} {np.std(differences):>10.2f}")
    print(f"{'最大成本差异':<20} {np.max(differences):>10.2f}")
    print(f"{'最小成本差异':<20} {np.min(differences):>10.2f}")
    print(f"{'平均相对误差':<20} {np.mean(relative_errors):>9.2f}%")
    print(f"{'相对误差标准差':<20} {np.std(relative_errors):>9.2f}%")
    
    # 7. 生成图表
    create_interactive_plot(detailed_info, start_step, num_steps)
    
    return detailed_info

def create_interactive_plot(detailed_info, start_step, num_steps):
    """创建交互式测试结果图表"""
    
    time_labels = [info['time_label'] for info in detailed_info]
    model_costs = [info['model_cost'] for info in detailed_info]
    optimal_costs = [info['optimal_cost'] for info in detailed_info]
    differences = [info['difference'] for info in detailed_info]
    relative_errors = [info['relative_error'] for info in detailed_info]
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    x_pos = range(len(time_labels))
    
    # 子图1: 成本对比曲线
    ax1.plot(x_pos, model_costs, 'b-o', linewidth=2, markersize=3, label='模型预测成本')
    ax1.plot(x_pos, optimal_costs, 'r--s', linewidth=2, markersize=3, label='CSV最优成本')
    ax1.set_title('逐时段运行成本对比')
    ax1.set_ylabel('运行成本 (美元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 成本差异柱状图
    colors = ['red' if x > 0 else 'green' for x in differences]
    ax2.bar(x_pos, differences, color=colors, alpha=0.7)
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)
    ax2.set_title('成本差异 (模型 - 最优)')
    ax2.set_ylabel('成本差异 (美元)')
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 相对误差
    ax3.bar(x_pos, relative_errors, color=colors, alpha=0.7)
    ax3.axhline(y=0, color='black', linestyle='-', linewidth=1)
    ax3.set_title('相对误差')
    ax3.set_ylabel('相对误差 (%)')
    ax3.set_xlabel('时间')
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 成本分布直方图
    ax4.hist(differences, bins=min(20, len(differences)//2), alpha=0.7, color='skyblue', edgecolor='black')
    ax4.axvline(x=0, color='red', linestyle='--', linewidth=2)
    ax4.axvline(x=np.mean(differences), color='orange', linestyle='-', linewidth=2, label=f'平均值: {np.mean(differences):.1f}')
    ax4.set_title('成本差异分布')
    ax4.set_xlabel('成本差异 (美元)')
    ax4.set_ylabel('频次')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 设置x轴标签 (只对前三个子图)
    for ax in [ax1, ax2, ax3]:
        step = max(1, len(time_labels) // 8)
        ax.set_xticks(x_pos[::step])
        ax.set_xticklabels(time_labels[::step], rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs('plots', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'interactive_cost_test_{start_step}_{num_steps}_{timestamp}.png'
    filepath = os.path.join('plots', filename)
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n📈 图表已保存: {filepath}")

def main():
    """主函数"""
    
    # 获取用户输入
    start_step, num_steps = get_user_input()
    
    if start_step is None:
        return
    
    # 运行测试
    results = run_interactive_test(start_step, num_steps)
    
    if results:
        print(f"\n✅ 测试完成! 共测试了{len(results)}个时间点")
        
        # 询问是否保存详细结果
        save_details = input("\n是否保存详细结果到CSV文件? (y/n): ").lower().strip()
        if save_details == 'y':
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f'cost_test_results_{start_step}_{num_steps}_{timestamp}.csv'
            
            df_results = pd.DataFrame(results)
            df_results.to_csv(csv_filename, index=False)
            print(f"✅ 详细结果已保存到: {csv_filename}")
    else:
        print("\n❌ 测试失败!")

if __name__ == "__main__":
    main()

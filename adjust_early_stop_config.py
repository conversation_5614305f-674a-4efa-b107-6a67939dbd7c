#!/usr/bin/env python3
"""
调整早停机制配置的辅助脚本
"""

import os
import sys

def modify_config(early_stop_enabled=None, cost_ratio_threshold=None, consecutive_episodes_threshold=None):
    """
    修改config.py中的早停机制配置
    
    Args:
        early_stop_enabled: 是否启用早停机制
        cost_ratio_threshold: 成本比阈值
        consecutive_episodes_threshold: 连续回合数阈值
    """
    config_file = 'config.py'
    
    if not os.path.exists(config_file):
        print(f"错误: 找不到配置文件 {config_file}")
        return False
    
    # 读取当前配置
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 准备替换的内容
    replacements = []
    
    if early_stop_enabled is not None:
        replacements.append((
            "'early_stop_enabled': True",
            f"'early_stop_enabled': {early_stop_enabled}"
        ))
        replacements.append((
            "'early_stop_enabled': False",
            f"'early_stop_enabled': {early_stop_enabled}"
        ))
    
    if cost_ratio_threshold is not None:
        # 查找当前的阈值设置
        import re
        pattern = r"'cost_ratio_threshold':\s*[\d.]+,"
        replacement = f"'cost_ratio_threshold': {cost_ratio_threshold},"
        content = re.sub(pattern, replacement, content)
    
    if consecutive_episodes_threshold is not None:
        import re
        pattern = r"'consecutive_episodes_threshold':\s*\d+,"
        replacement = f"'consecutive_episodes_threshold': {consecutive_episodes_threshold},"
        content = re.sub(pattern, replacement, content)
    
    # 应用其他替换
    for old, new in replacements:
        content = content.replace(old, new)
    
    # 写回文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 配置已更新")
    return True

def show_current_config():
    """显示当前配置"""
    try:
        # 重新导入配置以获取最新值
        if 'config' in sys.modules:
            del sys.modules['config']
        
        from config import TRAIN_CONFIG
        
        print("当前早停机制配置:")
        print(f"  启用状态: {TRAIN_CONFIG.get('early_stop_enabled', False)}")
        print(f"  成本比阈值: {TRAIN_CONFIG.get('cost_ratio_threshold', 0.9)}")
        print(f"  连续回合阈值: {TRAIN_CONFIG.get('consecutive_episodes_threshold', 30)}")
        print(f"  总训练回合数: {TRAIN_CONFIG.get('num_episodes', 600)}")
        
    except ImportError as e:
        print(f"错误: 无法导入配置 - {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("早停机制配置调整工具")
    print("=" * 60)
    
    while True:
        print("\n当前配置:")
        show_current_config()
        
        print("\n选择操作:")
        print("1. 启用早停机制")
        print("2. 禁用早停机制")
        print("3. 调整成本比阈值")
        print("4. 调整连续回合阈值")
        print("5. 预设配置 - 严格模式 (阈值0.85, 连续20回合)")
        print("6. 预设配置 - 标准模式 (阈值0.90, 连续30回合)")
        print("7. 预设配置 - 宽松模式 (阈值0.95, 连续50回合)")
        print("8. 退出")
        
        choice = input("\n请输入选择 (1-8): ").strip()
        
        if choice == '1':
            modify_config(early_stop_enabled=True)
        elif choice == '2':
            modify_config(early_stop_enabled=False)
        elif choice == '3':
            try:
                threshold = float(input("请输入新的成本比阈值 (建议范围 0.8-0.95): "))
                if 0.5 <= threshold <= 1.0:
                    modify_config(cost_ratio_threshold=threshold)
                else:
                    print("⚠️ 阈值超出建议范围，但已应用")
                    modify_config(cost_ratio_threshold=threshold)
            except ValueError:
                print("❌ 输入无效，请输入数字")
        elif choice == '4':
            try:
                episodes = int(input("请输入新的连续回合阈值 (建议范围 10-100): "))
                if episodes > 0:
                    modify_config(consecutive_episodes_threshold=episodes)
                else:
                    print("❌ 回合数必须大于0")
            except ValueError:
                print("❌ 输入无效，请输入整数")
        elif choice == '5':
            # 严格模式
            modify_config(early_stop_enabled=True, cost_ratio_threshold=0.85, consecutive_episodes_threshold=20)
            print("✅ 已设置为严格模式")
        elif choice == '6':
            # 标准模式
            modify_config(early_stop_enabled=True, cost_ratio_threshold=0.90, consecutive_episodes_threshold=30)
            print("✅ 已设置为标准模式")
        elif choice == '7':
            # 宽松模式
            modify_config(early_stop_enabled=True, cost_ratio_threshold=0.95, consecutive_episodes_threshold=50)
            print("✅ 已设置为宽松模式")
        elif choice == '8':
            print("退出配置工具")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()

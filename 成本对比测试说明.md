# 最佳模型成本对比测试程序使用说明

## 概述

我为您创建了完整的成本对比测试系统，支持17473个时间点的数据分析，包含以下程序：

1. **`smart_time_selector.py`** - 智能时间选择器（**最推荐使用**）
2. **`interactive_cost_test.py`** - 交互式测试程序（改进版界面）
3. **`quick_cost_test.py`** - 快速测试程序（适合快速验证）
4. **`cost_comparison_test.py`** - 命令行测试程序（支持参数调用）
5. **`run_cost_test.py`** - 测试启动器（统一入口）

## 程序功能

### 主要功能
- 加载训练好的最佳GNN-DDPG模型
- 运行连续96个时间步（或自定义时间步数）的测试
- 对比模型预测成本与CSV文件中的真实最优成本
- 生成详细的对比图表和统计分析
- 支持选择任意起始时刻

### 输出内容
- **成本对比曲线图**: 显示模型成本vs最优成本的时间序列
- **成本差异图**: 显示每个时间点的成本差异
- **统计信息**: 平均差异、标准差、最大/最小差异、相对误差等
- **详细数据**: 可选择保存到CSV文件

## 数据规模

- **总时间点数**: 17473个
- **完整天数**: 182天
- **时间分辨率**: 15分钟间隔
- **数据总时长**: 约182.3天

## 使用方法

### 🎯 方法1: 统一启动器（最简单）

```bash
python run_cost_test.py
```

**特点:**
- 统一入口，选择不同的测试方式
- 提供使用说明和参数参考
- 适合初次使用者

### 🌟 方法2: 智能时间选择器（最推荐）

```bash
python smart_time_selector.py
```

**特点:**
- 最直观的时间点选择方式
- 支持按日期时间选择（如：第5天 15:30）
- 支持直接输入时间点编号
- 支持选择特殊时间段（早高峰、晚高峰等）
- 支持随机选择时间点
- 提供数据分布信息查看
- 选择后可直接运行测试

**使用步骤:**
1. 运行程序，选择时间点选择方式
2. 按提示选择具体时间点
3. 选择测试时长
4. 确认参数后开始测试
5. 查看结果和图表

### 📊 方法3: 交互式测试（改进版）

```bash
python interactive_cost_test.py
```

**特点:**
- 改进的用户界面，支持17473个时间点
- 三种时间选择方式：智能选择、直接输入、快速选择
- 生成4个子图的详细分析图表
- 可选择保存详细结果到CSV
- 支持更大的测试时长（最多200个时间点）

### 方法2: 快速测试

```bash
python quick_cost_test.py
```

**特点:**
- 预设参数，快速运行
- 默认测试4小时（16个时间点）
- 可以修改代码中的参数来改变测试时间段

**修改参数:**
在`quick_cost_test.py`的`main()`函数中修改：
```python
START_HOUR = 0      # 起始小时 (0-23)
DURATION_HOURS = 4  # 测试持续小时数
```

### 方法3: 命令行测试

```bash
# 默认从第0个时间步开始，测试96个时间步
python cost_comparison_test.py

# 自定义起始时间步和测试时长
python cost_comparison_test.py --start 100 --steps 48
```

**参数说明:**
- `--start`: 起始时间步（默认0）
- `--steps`: 测试时间步数量（默认96）

## 时间点说明

### 时间分辨率
- 每个时间点 = 15分钟
- 每小时 = 4个时间点
- 每天 = 96个时间点
- 总共 = 17473个时间点（约182天）

### 时间点计算公式
```
时间点 = (天数-1) × 96 + 小时 × 4 + 分钟÷15
```

### 时间点对应关系示例
```
时间点 0     -> 第1天 00:00
时间点 4     -> 第1天 01:00
时间点 28    -> 第1天 07:00 (早高峰)
时间点 48    -> 第1天 12:00 (午高峰)
时间点 72    -> 第1天 18:00 (晚高峰)
时间点 96    -> 第2天 00:00
时间点 192   -> 第3天 00:00
时间点 1000  -> 第11天 10:00
时间点 17472 -> 最后一个时间点
```

### 常用时间段对应的时间点
- **早高峰 (07:00)**: 第N天的时间点 = (N-1)×96 + 28
- **午高峰 (12:00)**: 第N天的时间点 = (N-1)×96 + 48
- **晚高峰 (18:00)**: 第N天的时间点 = (N-1)×96 + 72
- **夜间低谷 (22:00)**: 第N天的时间点 = (N-1)×96 + 88

## 输出文件

### 图表文件
- 保存在 `plots/` 目录下
- 文件名格式: `*_cost_comparison_*.png`
- 包含时间戳以避免覆盖

### 数据文件（可选）
- CSV格式的详细测试结果
- 包含每个时间点的详细信息
- 文件名格式: `cost_test_results_*.csv`

## 图表说明

### 交互式测试图表（4个子图）
1. **成本对比曲线**: 模型成本vs最优成本的时间序列
2. **成本差异柱状图**: 每个时间点的成本差异（正值=模型成本更高）
3. **相对误差**: 成本差异的百分比表示
4. **差异分布直方图**: 成本差异的统计分布

### 快速测试图表（2个子图）
1. **成本对比曲线**: 模型成本vs最优成本
2. **成本差异**: 带统计信息的差异柱状图

## 统计指标说明

- **模型平均成本**: 模型预测的平均运行成本
- **最优平均成本**: CSV文件中的平均最优成本
- **平均成本差异**: 模型成本与最优成本的平均差异
- **成本差异标准差**: 差异的波动程度
- **相对误差**: 差异相对于最优成本的百分比
- **最大/最小差异**: 差异的极值

## 注意事项

1. **模型文件**: 确保 `models/best_gnn_model.pth` 存在
2. **数据文件**: 确保 `data/power_schedule_with_wind_sequential.csv` 存在
3. **输出目录**: 程序会自动创建 `plots/` 目录
4. **GPU支持**: 程序会自动检测并使用可用的GPU
5. **中文显示**: 图表支持中文标签显示

## 推荐使用流程

1. **首次使用**: 运行 `interactive_cost_test.py`，选择小时长（如1-4小时）进行快速验证
2. **详细分析**: 选择感兴趣的时间段（如高峰时段）进行详细测试
3. **全天测试**: 运行24小时完整测试以获得全面评估
4. **结果保存**: 保存重要的测试结果和图表用于分析

## 故障排除

### 常见问题
1. **找不到模型文件**: 确保已完成训练并生成了最佳模型
2. **找不到数据文件**: 检查CSV文件路径是否正确
3. **内存不足**: 减少测试时长或使用较小的批次
4. **图表显示问题**: 检查matplotlib和中文字体安装

### 解决方案
- 检查文件路径和权限
- 确保所有依赖包已正确安装
- 查看控制台错误信息进行调试

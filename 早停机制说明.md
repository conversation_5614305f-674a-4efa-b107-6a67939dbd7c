# 基于成本比的早停机制说明

## 概述

本项目已实现基于成本比的训练早停机制。当连续多个回合的成本比超过设定阈值时，训练将自动停止并生成相应的图像和统计信息。

## 配置参数

在 `config.py` 文件的 `TRAIN_CONFIG` 中添加了以下配置项：

```python
TRAIN_CONFIG = {
    'num_episodes': 600, 
    'verbose': VERBOSE_OUTPUT,
    
    # 基于成本比的早停机制配置
    'early_stop_enabled': True,  # 是否启用早停机制
    'cost_ratio_threshold': 0.9,  # 成本比阈值
    'consecutive_episodes_threshold': 30,  # 连续回合数阈值
}
```

### 参数说明

- **`early_stop_enabled`**: 布尔值，控制是否启用早停机制
  - `True`: 启用早停机制
  - `False`: 禁用早停机制，进行完整训练

- **`cost_ratio_threshold`**: 浮点数，成本比阈值
  - 建议范围: 0.8 - 0.95
  - 当前设置: 0.9
  - 含义: 当成本比 (最优成本/实际成本) 超过此值时开始计数

- **`consecutive_episodes_threshold`**: 整数，连续回合数阈值
  - 建议范围: 20 - 50
  - 当前设置: 30
  - 含义: 连续多少个回合成本比超过阈值时触发早停

## 工作原理

1. **成本比计算**: 每个回合结束后，计算 `成本比 = 最优成本 / 实际成本`
2. **阈值检查**: 如果成本比 > 阈值，连续计数器 +1；否则重置为 0
3. **早停触发**: 当连续计数器达到设定阈值时，触发早停
4. **训练终止**: 保存当前模型，生成图像，记录统计信息

## 输出信息

### 训练过程中的提示

- **正常情况**: `✅ 成本比 0.8500 <= 0.9, 重置连续计数`
- **超过阈值**: `⚠️ 成本比 0.9200 > 0.9, 连续计数: 5/30`
- **触发早停**: `🛑 早停触发! 连续30个回合成本比大于0.9`

### 生成的图像

训练结束后会生成包含早停信息的成本比率曲线图：
- 红色虚线: 早停阈值线
- 红色实线: 早停触发点
- 红色散点: 早停位置标记

### 日志记录

在训练日志中会记录：
- 早停机制配置信息
- 早停触发状态
- 成本比统计信息

## 使用示例

### 启用早停机制

```python
# 在 config.py 中设置
TRAIN_CONFIG = {
    'early_stop_enabled': True,
    'cost_ratio_threshold': 0.9,
    'consecutive_episodes_threshold': 30,
    # ... 其他配置
}
```

### 禁用早停机制

```python
# 在 config.py 中设置
TRAIN_CONFIG = {
    'early_stop_enabled': False,
    # ... 其他配置
}
```

## 参数调优建议

### 成本比阈值 (cost_ratio_threshold)

- **0.85**: 较严格，适合对成本控制要求很高的场景
- **0.90**: 中等严格，平衡训练效率和成本控制
- **0.95**: 较宽松，允许更多的训练时间

### 连续回合阈值 (consecutive_episodes_threshold)

- **20**: 快速响应，适合快速验证
- **30**: 平衡设置，避免偶然波动
- **50**: 保守设置，确保稳定性

## 测试验证

运行测试脚本验证配置：

```bash
python test_early_stop.py
```

该脚本会：
1. 验证配置参数的合理性
2. 模拟早停场景
3. 显示配置信息和预期行为

## 注意事项

1. **成本比数据依赖**: 早停机制依赖于最优成本数据，确保 `optimal_cost` 在训练数据中可用
2. **最早停止时间**: 最早可能在第 `consecutive_episodes_threshold` 回合触发早停
3. **模型保存**: 触发早停时会自动保存当前最佳模型
4. **图像生成**: 即使提前停止，也会生成完整的训练曲线图

## 故障排除

### 早停未触发
- 检查 `early_stop_enabled` 是否为 `True`
- 确认成本比数据是否正确计算
- 调整阈值参数

### 过早触发早停
- 增大 `cost_ratio_threshold`
- 增大 `consecutive_episodes_threshold`
- 检查成本比计算逻辑

### 成本比数据缺失
- 确保训练数据中包含最优成本信息
- 检查 `OptimalActionProvider` 是否正确加载
